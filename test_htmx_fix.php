<?php
/**
 * Test script to verify the HTMX fix
 */

echo "Testing HTMX Configuration Fix\n";
echo "===============================\n\n";

// Test 1: Check if files have correct syntax
echo "1. Testing file syntax...\n";
$output = shell_exec("php -l system/components/edges/email-template-editor.edge.php 2>&1");
if (strpos($output, 'No syntax errors') !== false) {
    echo "   ✓ email-template-editor.edge.php - syntax OK\n";
} else {
    echo "   ✗ email-template-editor.edge.php - syntax error: $output\n";
}
echo "\n";

// Test 2: Check if HTMX configuration was fixed
echo "2. Testing HTMX configuration...\n";
$template_content = file_get_contents('system/components/edges/email-template-editor.edge.php');

// Check for the problematic pattern
if (strpos($template_content, 'js:this.value') !== false) {
    echo "   ⚠ Still using js:this.value (might cause issues)\n";
} else {
    echo "   ✓ js:this.value removed\n";
}

// Check for correct patterns
$correct_patterns = [
    'name="data_source_id"' => 'Form field name attribute',
    'hx-include="this"' => 'HTMX include directive',
    'hx-post=' => 'HTMX POST request',
    'hx-target=' => 'HTMX target element',
    'hx-trigger="change"' => 'HTMX change trigger'
];

foreach ($correct_patterns as $pattern => $description) {
    if (strpos($template_content, $pattern) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

// Test 3: Check debug information in field browser
echo "3. Testing debug information...\n";
$browser_content = file_get_contents('system/components/edges/data-source-field-browser.edge.php');

if (strpos($browser_content, 'Debug Info:') !== false) {
    echo "   ✓ Debug information enabled\n";
} else {
    echo "   ✗ Debug information not found\n";
}

if (strpos($browser_content, 'Data Source ID: {{ $data_source_id }}') !== false) {
    echo "   ✓ Data source ID debugging enabled\n";
} else {
    echo "   ✗ Data source ID debugging not found\n";
}
echo "\n";

echo "✅ HTMX configuration fix verification completed!\n\n";

echo "What was fixed:\n";
echo "===============\n";
echo "❌ BEFORE: hx-vals='{\"data_source_id\": \"js:this.value\"}'\n";
echo "✅ AFTER: name=\"data_source_id\" + hx-include=\"this\"\n\n";

echo "How HTMX works now:\n";
echo "===================\n";
echo "1. User selects data source from dropdown\n";
echo "2. HTMX detects 'change' event on select element\n";
echo "3. HTMX includes the select element (hx-include=\"this\")\n";
echo "4. HTMX sends POST request with data_source_id parameter\n";
echo "5. API receives actual data source ID (e.g., '1')\n";
echo "6. Component processes the correct data source\n\n";

echo "Expected behavior now:\n";
echo "======================\n";
echo "1. Open email template editor\n";
echo "2. Select 'Autodesk_autorenew (autodesk_subscriptions)' from dropdown\n";
echo "3. Debug info should show:\n";
echo "   - Data Source ID: 1 (not 'js:this.value')\n";
echo "   - Data Source Name: Autodesk_autorenew\n";
echo "   - Table Name: autodesk_subscriptions\n";
echo "   - Selected Columns Type: string\n";
echo "4. Should see 17 fields from both tables\n\n";

echo "If it still doesn't work, the debug info will show the actual issue.\n";
?>
